#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约每根K线实体涨跌幅计算工具

功能：
1. 读取507个USDT永续合约的历史数据
2. 计算每根K线的实体涨跌幅（收盘价-开盘价）/开盘价
3. 计算实体大小、上下影线等技术指标
4. 保存为多种格式文件（CSV、Parquet、JSON）
5. 生成统计分析报告

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

class KlineBodyCalculator:
    """507合约K线实体涨跌幅计算器"""
    
    def __init__(self):
        """初始化计算器"""
        self.data_dirs = [
            "all_usdt_perpetual_history/raw_data",
            "perpetual_historical/processed",
            "sample_507_data"
        ]
        self.output_dir = "507_kline_body_analysis"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "individual_contracts"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "summary"), exist_ok=True)
        
        self.stats = {
            'total_contracts': 0,
            'processed_contracts': 0,
            'failed_contracts': 0,
            'total_klines': 0,
            'start_time': datetime.now().isoformat()
        }
        
        print("🚀 507合约K线实体涨跌幅计算器启动")
        print("=" * 60)
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🔍 数据源目录: {self.data_dirs}")
    
    def find_data_files(self) -> List[Dict]:
        """查找所有可用的数据文件"""
        print("\n📋 扫描数据文件...")
        
        data_files = []
        
        for data_dir in self.data_dirs:
            if not os.path.exists(data_dir):
                continue
                
            print(f"📂 扫描目录: {data_dir}")
            
            for file in os.listdir(data_dir):
                if file.endswith('.csv') and 'USDT' in file:
                    symbol = file.replace('_history.csv', '').replace('_processed.csv', '').replace('_sample.csv', '')
                    
                    if symbol.endswith('USDT'):
                        file_path = os.path.join(data_dir, file)
                        file_size = os.path.getsize(file_path)
                        
                        data_files.append({
                            'symbol': symbol,
                            'file_path': file_path,
                            'file_size': file_size,
                            'source_dir': data_dir
                        })
        
        # 去重（优先选择文件大小最大的）
        unique_files = {}
        for file_info in data_files:
            symbol = file_info['symbol']
            if symbol not in unique_files or file_info['file_size'] > unique_files[symbol]['file_size']:
                unique_files[symbol] = file_info
        
        final_files = list(unique_files.values())
        final_files.sort(key=lambda x: x['symbol'])
        
        print(f"✅ 找到 {len(final_files)} 个合约数据文件")
        self.stats['total_contracts'] = len(final_files)
        
        return final_files
    
    def calculate_kline_body_metrics(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        计算K线实体相关指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            symbol: 合约符号
            
        Returns:
            pd.DataFrame: 包含实体指标的数据框
        """
        try:
            # 确保数据列存在
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                print(f"❌ {symbol}: 缺少必要的数据列")
                return None
            
            # 创建结果数据框
            result_df = df.copy()
            
            # 1. 实体大小（收盘价 - 开盘价）
            result_df['body_size'] = result_df['close'] - result_df['open']
            
            # 2. 实体涨跌幅（实体大小 / 开盘价）
            result_df['body_change_pct'] = (result_df['body_size'] / result_df['open']) * 100
            
            # 3. 实体绝对大小
            result_df['body_abs_size'] = abs(result_df['body_size'])
            
            # 4. 实体绝对涨跌幅
            result_df['body_abs_change_pct'] = abs(result_df['body_change_pct'])
            
            # 5. K线类型
            result_df['candle_type'] = result_df['body_size'].apply(
                lambda x: '阳线' if x > 0 else ('阴线' if x < 0 else '十字星')
            )
            
            # 6. 上影线长度
            result_df['upper_shadow'] = result_df[['open', 'close']].max(axis=1) - result_df['high']
            result_df['upper_shadow'] = result_df['upper_shadow'].abs()  # 修正：上影线应该是正值
            result_df['upper_shadow'] = result_df['high'] - result_df[['open', 'close']].max(axis=1)
            
            # 7. 下影线长度  
            result_df['lower_shadow'] = result_df[['open', 'close']].min(axis=1) - result_df['low']
            
            # 8. 上下影线百分比
            result_df['upper_shadow_pct'] = (result_df['upper_shadow'] / result_df['open']) * 100
            result_df['lower_shadow_pct'] = (result_df['lower_shadow'] / result_df['open']) * 100
            
            # 9. 总振幅
            result_df['total_range'] = result_df['high'] - result_df['low']
            result_df['total_range_pct'] = (result_df['total_range'] / result_df['open']) * 100
            
            # 10. 实体占总振幅比例
            result_df['body_ratio'] = result_df['body_abs_size'] / result_df['total_range']
            result_df['body_ratio'] = result_df['body_ratio'].fillna(0)  # 处理除零情况
            
            # 11. 日收益率（传统计算方式）
            result_df['daily_return_pct'] = ((result_df['close'] - result_df['open']) / result_df['open']) * 100
            
            # 12. 价格位置指标
            result_df['close_position'] = (result_df['close'] - result_df['low']) / (result_df['high'] - result_df['low'])
            result_df['close_position'] = result_df['close_position'].fillna(0.5)  # 处理除零情况
            
            # 13. 添加合约信息
            result_df['symbol'] = symbol
            
            # 14. 确保时间列存在
            if 'timestamp' in result_df.columns:
                result_df['date'] = pd.to_datetime(result_df['timestamp']).dt.date
            elif 'date' not in result_df.columns:
                # 如果没有时间信息，创建一个序号
                result_df['date'] = range(len(result_df))
            
            print(f"✅ {symbol}: 计算完成，{len(result_df)} 根K线")
            
            return result_df
            
        except Exception as e:
            print(f"❌ {symbol}: 计算失败 - {str(e)}")
            return None
    
    def save_contract_data(self, df: pd.DataFrame, symbol: str) -> bool:
        """
        保存单个合约的K线实体数据
        
        Args:
            df: 计算后的数据框
            symbol: 合约符号
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 选择要保存的列
            save_columns = [
                'symbol', 'date', 'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'body_size', 'body_change_pct', 'body_abs_size', 'body_abs_change_pct',
                'candle_type', 'upper_shadow', 'lower_shadow', 
                'upper_shadow_pct', 'lower_shadow_pct',
                'total_range', 'total_range_pct', 'body_ratio',
                'daily_return_pct', 'close_position'
            ]
            
            # 只保存存在的列
            available_columns = [col for col in save_columns if col in df.columns]
            df_save = df[available_columns].copy()
            
            # 保存CSV文件
            csv_file = os.path.join(self.output_dir, "individual_contracts", f"{symbol}_kline_body.csv")
            df_save.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 生成统计信息
            stats = {
                'symbol': symbol,
                'total_klines': len(df_save),
                'date_range': {
                    'start': str(df_save['date'].min()) if 'date' in df_save.columns else 'N/A',
                    'end': str(df_save['date'].max()) if 'date' in df_save.columns else 'N/A'
                },
                'body_change_stats': {
                    'mean': float(df_save['body_change_pct'].mean()),
                    'std': float(df_save['body_change_pct'].std()),
                    'min': float(df_save['body_change_pct'].min()),
                    'max': float(df_save['body_change_pct'].max()),
                    'median': float(df_save['body_change_pct'].median())
                },
                'candle_type_counts': df_save['candle_type'].value_counts().to_dict(),
                'body_ratio_stats': {
                    'mean': float(df_save['body_ratio'].mean()),
                    'median': float(df_save['body_ratio'].median())
                },
                'file_info': {
                    'csv_file': csv_file,
                    'file_size_mb': round(os.path.getsize(csv_file) / 1024 / 1024, 2)
                },
                'calculation_time': datetime.now().isoformat()
            }
            
            # 保存统计信息
            stats_file = os.path.join(self.output_dir, "individual_contracts", f"{symbol}_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            print(f"💾 {symbol}: 数据已保存 ({len(df_save)} 根K线)")
            
            return True
            
        except Exception as e:
            print(f"❌ {symbol}: 保存失败 - {str(e)}")
            return False
    
    def process_all_contracts(self) -> bool:
        """处理所有合约数据"""
        try:
            print(f"\n🚀 开始处理507合约K线实体涨跌幅计算...")
            
            # 查找数据文件
            data_files = self.find_data_files()
            if not data_files:
                print("❌ 未找到任何数据文件")
                return False
            
            print(f"\n📊 准备处理 {len(data_files)} 个合约")
            print("=" * 60)
            
            all_results = []
            
            for i, file_info in enumerate(data_files, 1):
                symbol = file_info['symbol']
                file_path = file_info['file_path']
                
                print(f"\n[{i}/{len(data_files)}] 处理 {symbol}...")
                
                try:
                    # 读取数据
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                    
                    if df.empty:
                        print(f"⚠️ {symbol}: 数据文件为空")
                        self.stats['failed_contracts'] += 1
                        continue
                    
                    # 计算K线实体指标
                    result_df = self.calculate_kline_body_metrics(df, symbol)
                    
                    if result_df is not None:
                        # 保存单个合约数据
                        if self.save_contract_data(result_df, symbol):
                            self.stats['processed_contracts'] += 1
                            self.stats['total_klines'] += len(result_df)
                            
                            # 收集汇总数据
                            summary_data = {
                                'symbol': symbol,
                                'total_klines': len(result_df),
                                'avg_body_change': result_df['body_change_pct'].mean(),
                                'max_body_change': result_df['body_change_pct'].max(),
                                'min_body_change': result_df['body_change_pct'].min(),
                                'avg_body_ratio': result_df['body_ratio'].mean(),
                                'yang_count': len(result_df[result_df['candle_type'] == '阳线']),
                                'yin_count': len(result_df[result_df['candle_type'] == '阴线']),
                                'cross_count': len(result_df[result_df['candle_type'] == '十字星'])
                            }
                            all_results.append(summary_data)
                        else:
                            self.stats['failed_contracts'] += 1
                    else:
                        self.stats['failed_contracts'] += 1
                
                except Exception as e:
                    print(f"❌ {symbol}: 处理失败 - {str(e)}")
                    self.stats['failed_contracts'] += 1
                
                # 显示进度
                progress = i / len(data_files) * 100
                print(f"📈 总进度: {progress:.1f}% ({i}/{len(data_files)})")
            
            # 生成汇总报告
            self.generate_summary_report(all_results)
            
            self.stats['end_time'] = datetime.now().isoformat()
            
            print(f"\n🎉 507合约K线实体涨跌幅计算完成！")
            print(f"✅ 成功处理: {self.stats['processed_contracts']} 个合约")
            print(f"❌ 处理失败: {self.stats['failed_contracts']} 个合约")
            print(f"📊 总K线数: {self.stats['total_klines']:,}")
            print(f"📁 输出目录: {self.output_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理过程中发生错误: {str(e)}")
            return False
    
    def generate_summary_report(self, all_results: List[Dict]):
        """生成汇总报告"""
        try:
            print(f"\n📝 生成汇总报告...")
            
            if not all_results:
                print("⚠️ 没有数据生成汇总报告")
                return
            
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_results)
            
            # 保存汇总CSV
            summary_csv = os.path.join(self.output_dir, "summary", "507_contracts_kline_body_summary.csv")
            summary_df.to_csv(summary_csv, index=False, encoding='utf-8-sig')
            
            # 保存汇总Parquet
            summary_parquet = os.path.join(self.output_dir, "summary", "507_contracts_kline_body_summary.parquet")
            summary_df.to_parquet(summary_parquet, compression='gzip', index=False)
            
            # 生成统计报告
            report = {
                'generation_time': datetime.now().isoformat(),
                'total_contracts': len(summary_df),
                'total_klines': int(summary_df['total_klines'].sum()),
                'overall_stats': {
                    'avg_body_change_mean': float(summary_df['avg_body_change'].mean()),
                    'avg_body_change_std': float(summary_df['avg_body_change'].std()),
                    'max_single_kline_gain': float(summary_df['max_body_change'].max()),
                    'max_single_kline_loss': float(summary_df['min_body_change'].min()),
                    'avg_body_ratio': float(summary_df['avg_body_ratio'].mean())
                },
                'candle_type_totals': {
                    'total_yang': int(summary_df['yang_count'].sum()),
                    'total_yin': int(summary_df['yin_count'].sum()),
                    'total_cross': int(summary_df['cross_count'].sum())
                },
                'top_performers': {
                    'highest_avg_body_change': summary_df.nlargest(10, 'avg_body_change')[['symbol', 'avg_body_change']].to_dict('records'),
                    'lowest_avg_body_change': summary_df.nsmallest(10, 'avg_body_change')[['symbol', 'avg_body_change']].to_dict('records'),
                    'most_klines': summary_df.nlargest(10, 'total_klines')[['symbol', 'total_klines']].to_dict('records')
                },
                'files': {
                    'summary_csv': summary_csv,
                    'summary_parquet': summary_parquet,
                    'individual_contracts_dir': os.path.join(self.output_dir, "individual_contracts")
                },
                'processing_stats': self.stats
            }
            
            # 保存报告
            report_file = os.path.join(self.output_dir, "summary", "analysis_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 汇总报告已生成:")
            print(f"📊 汇总CSV: {summary_csv}")
            print(f"📊 汇总Parquet: {summary_parquet}")
            print(f"📝 分析报告: {report_file}")
            
        except Exception as e:
            print(f"❌ 生成汇总报告失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 启动507合约K线实体涨跌幅计算...")
    
    calculator = KlineBodyCalculator()
    
    success = calculator.process_all_contracts()
    
    if success:
        print("\n✅ 计算完成！")
        print(f"📁 所有结果保存在: {calculator.output_dir}")
        print(f"📋 个别合约数据: {calculator.output_dir}/individual_contracts/")
        print(f"📊 汇总数据: {calculator.output_dir}/summary/")
    else:
        print("\n❌ 计算失败！")
    
    print("\n按回车键退出...")
    input()
    
    return success

if __name__ == "__main__":
    main()
